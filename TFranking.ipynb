import sys
import os
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
import json
from datetime import datetime

# 最新のプロセッサクラスをインポート
from module.race_batch_processor import process_race_bin_to_pickle_batch
from module.race_horse_targeted_processor import RaceHorseTargetedProcessor
from module.race_data_processor import RaceProcessor
from module.data_merger import <PERSON>Merger

import pandas as pd
import numpy as np
from tqdm.notebook import tqdm

comprehensive_df = pd.read_csv('dataframe.csv')
comprehensive_df

from datetime import timedelta
import pandas as pd # pandas をインポート
import numpy as np # numpy をインポート (ダミーデータ用)
from module.constants import HorseInfoCols # Assuming HorseInfoCols.BIRTHDAY is defined

# Define column names as constants for better maintainability
ACTUAL_RANK_COLUMN_DEFAULT = '着順' # Default Japanese column name
ACTUAL_RANK_COLUMN_FALLBACK = 'rank' # Fallback English column name
RACE_ID_COLUMN = 'race_id'
HORSE_ID_COLUMN = 'horse_id'
DATE_COLUMN = 'date'

# Determine the actual rank column name to be used
actual_rank_column_name = ACTUAL_RANK_COLUMN_DEFAULT
if actual_rank_column_name not in comprehensive_df.columns:
    if ACTUAL_RANK_COLUMN_FALLBACK in comprehensive_df.columns:
        actual_rank_column_name = ACTUAL_RANK_COLUMN_FALLBACK
        print(f"Warning: Column '{ACTUAL_RANK_COLUMN_DEFAULT}' not found. Using '{actual_rank_column_name}' column instead.")
    else:
        raise KeyError(f"Required rank column ('{ACTUAL_RANK_COLUMN_DEFAULT}' or '{ACTUAL_RANK_COLUMN_FALLBACK}') for relevance score generation not found in DataFrame.")

# 1. Convert rank column to numeric
comprehensive_df[actual_rank_column_name] = pd.to_numeric(comprehensive_df[actual_rank_column_name], errors='coerce')

# Handle NaNs after conversion (e.g., fill with a large number, or drop rows)
if comprehensive_df[actual_rank_column_name].isnull().any():
    print(f"Warning: Column '{actual_rank_column_name}' contains non-numeric values that were converted to NaN. Handle these NaNs as appropriate (e.g., imputation, exclusion).")
    # Example: comprehensive_df[actual_rank_column_name] = comprehensive_df[actual_rank_column_name].fillna(99) # Fill with a large rank

# 2. Calculate relevance score
NUM_STARTERS_COLUMN = 'num_starters_in_race'
RELEVANCE_SCORE_COLUMN = 'relevance_score'

comprehensive_df[NUM_STARTERS_COLUMN] = comprehensive_df.groupby(RACE_ID_COLUMN)[actual_rank_column_name].transform('max')
comprehensive_df[RELEVANCE_SCORE_COLUMN] = comprehensive_df[NUM_STARTERS_COLUMN] - comprehensive_df[actual_rank_column_name] + 1
y_label_column_name = RELEVANCE_SCORE_COLUMN # This will be the target variable for ranking

print("--- After relevance score generation ---")
print(comprehensive_df[[RACE_ID_COLUMN, HORSE_ID_COLUMN, actual_rank_column_name, NUM_STARTERS_COLUMN, y_label_column_name]].head())

# Feature selection
columns_to_exclude = [
    RACE_ID_COLUMN, HORSE_ID_COLUMN, DATE_COLUMN, y_label_column_name, actual_rank_column_name,
    'last_race_date',      # Exclude datetime64 (assuming interval_days is used)
    HorseInfoCols.BIRTHDAY # Exclude datetime64 (assuming age column is used)
]
# If 'rank' (ACTUAL_RANK_COLUMN_FALLBACK) exists and is different from actual_rank_column_name, exclude it too.
if ACTUAL_RANK_COLUMN_FALLBACK in comprehensive_df.columns and ACTUAL_RANK_COLUMN_FALLBACK != actual_rank_column_name:
    columns_to_exclude.append(ACTUAL_RANK_COLUMN_FALLBACK)

# Consider excluding NUM_STARTERS_COLUMN to prevent target leakage,
# though it might be a useful feature indicating race scale.
# If needed, add: columns_to_exclude.append(NUM_STARTERS_COLUMN)

feature_columns = [
    col for col in comprehensive_df.columns
        if col not in columns_to_exclude
]
print(f"Features to be used: {feature_columns}")

# Missing value imputation (handle based on data type)
# Imputation strategies:
#  - Constant value for numeric NaNs (ensure it's outside the typical range)
#  - Indicator value for strings (or consider separate unknown category handling)
MISSING_NUMERIC_FILL = 999.0  # Use a value larger than any reasonable rank
MISSING_CATEGORY_FILL = "Unknown"  # Or perhaps "" (empty string), depending on the context
MISSING_OBJECT_FILL = "Unknown" # For object types that fail numeric conversion


for col in feature_columns:
    col_dtype = comprehensive_df[col].dtype # Store dtype for re-use
    # Check if it's a string
    # Note: Python 3.8 compatibility: Using isinstance instead of == pd.StringDtype() to avoid TypeErrors

    if isinstance(col_dtype, pd.StringDtype):
        comprehensive_df[col] = comprehensive_df[col].fillna(MISSING_CATEGORY_FILL)

    elif pd.api.types.is_numeric_dtype(comprehensive_df[col].dtype):
        comprehensive_df[col] = comprehensive_df[col].fillna(MISSING_NUMERIC_FILL)
    elif col_dtype == 'object':
        # For object type, attempt numeric conversion first

        try:
            comprehensive_df[col] = pd.to_numeric(comprehensive_df[col], errors='coerce').fillna(MISSING_NUMERIC_FILL)
            # Check if it's categorical if the number of unique values is small
            # This is only a simple check, and depending on the data, may need to be more robust
            if comprehensive_df[col].nunique() < 10:
                print(f"Info: Column '{col}' originally of type 'object' was converted to numeric and might be categorical. Consider one-hot encoding.")

        except Exception:
            # If not convertible to numeric, treat as categorical string
            comprehensive_df[col] = comprehensive_df[col].fillna(MISSING_OBJECT_FILL)
    # Handle other dtypes (e.g., datetime if not already excluded) as needed

# --- 2. Data Splitting (time-series aware, automatic split date determination) ---
if DATE_COLUMN not in comprehensive_df.columns:
    raise KeyError(f"Required column '{DATE_COLUMN}' for data splitting not found in DataFrame.")
if not pd.api.types.is_datetime64_any_dtype(comprehensive_df[DATE_COLUMN]): # Handle possible non-datetime type
    comprehensive_df[DATE_COLUMN] = pd.to_datetime(comprehensive_df[DATE_COLUMN], errors='coerce')
    if comprehensive_df[DATE_COLUMN].isnull().any():
        print(f"Warning: Column '{DATE_COLUMN}' contains values that could not be converted to datetime and resulted in NaT.")
        # Depending on the use case, either raise an error or drop/impute these NaTs
        # comprehensive_df.dropna(subset=[DATE_COLUMN], inplace=True)

if comprehensive_df.empty or comprehensive_df[DATE_COLUMN].isnull().all():
    raise ValueError("DataFrame is empty or contains no valid date data for splitting.")

# 利用可能なデータの最大日付を取得
max_data_date = comprehensive_df[DATE_COLUMN].max()

# 分割戦略: 例えば、最新のN日間をテストデータとする
TEST_PERIOD_DAYS = 30  # Example: last 30 days for testing
MIN_TRAIN_RACES = 50 # Increased minimum number of races for training
MIN_TEST_RACES = 10  # Increased minimum number of races for testing

if pd.isna(max_data_date):
    raise ValueError("最大日付が取得できませんでした。")

# split_date をここで定義
split_date = max_data_date - timedelta(days=TEST_PERIOD_DAYS)

# 全レース数を取得
total_unique_races = comprehensive_df[RACE_ID_COLUMN].nunique()

# 学習データとテストデータのレース数を計算
potential_train_races = comprehensive_df[comprehensive_df['date'] < split_date]['race_id'].nunique()
potential_test_races = comprehensive_df[comprehensive_df['date'] >= split_date]['race_id'].nunique()

potential_train_races = comprehensive_df[comprehensive_df[DATE_COLUMN] < split_date][RACE_ID_COLUMN].nunique()
potential_test_races = comprehensive_df[comprehensive_df[DATE_COLUMN] >= split_date][RACE_ID_COLUMN].nunique()

unique_dates = sorted(comprehensive_df[DATE_COLUMN].dropna().unique())

if len(unique_dates) <= 1:
    print("Warning: Only one unique date found in data. Cannot split into train/test. Using all data for training.")
    split_date = max_data_date + timedelta(days=1) # Ensure all data goes to train
elif potential_train_races < MIN_TRAIN_RACES and total_unique_races > (MIN_TRAIN_RACES + MIN_TEST_RACES):
    print(f"Warning: Initial split_date ({split_date.strftime('%Y-%m-%d')}) results in too few training races ({potential_train_races}). Adjusting split_date.")
    # Try to adjust split_date to ensure MIN_TRAIN_RACES, while keeping at least MIN_TEST_RACES
    # This can be complex; a simpler approach is to iterate dates backwards from max_data_date
    # until MIN_TEST_RACES are in the test set, then check if MIN_TRAIN_RACES remain.
    # For now, we'll keep the warning and proceed, or use a simpler adjustment.
    # Simplified adjustment: if train is too small, try to make test smaller if possible
    if len(unique_dates) > MIN_TEST_RACES : # Ensure we can even form a minimal test set
        # Find a date that gives at least MIN_TEST_RACES for testing
        for i in range(len(unique_dates) - MIN_TEST_RACES, 0, -1):
            temp_split_date = pd.to_datetime(unique_dates[i])
            if comprehensive_df[comprehensive_df[DATE_COLUMN] < temp_split_date][RACE_ID_COLUMN].nunique() >= MIN_TRAIN_RACES:
                split_date = temp_split_date
                print(f"Adjusted split_date to {split_date.strftime('%Y-%m-%d')} to secure more training data.")
                break
elif potential_test_races < MIN_TEST_RACES and total_unique_races > (MIN_TRAIN_RACES + MIN_TEST_RACES):
    print(f"Warning: Initial split_date ({split_date.strftime('%Y-%m-%d')}) results in too few test races ({potential_test_races}). Adjusting split_date.")
    # Try to adjust split_date to ensure MIN_TEST_RACES
    if len(unique_dates) > MIN_TRAIN_RACES :
         for i in range(MIN_TRAIN_RACES, len(unique_dates) - MIN_TEST_RACES +1):
            temp_split_date = pd.to_datetime(unique_dates[i])
            if comprehensive_df[comprehensive_df[DATE_COLUMN] >= temp_split_date][RACE_ID_COLUMN].nunique() >= MIN_TEST_RACES:
                split_date = temp_split_date
                print(f"Adjusted split_date to {split_date.strftime('%Y-%m-%d')} to secure more test data.")
                break
elif comprehensive_df[DATE_COLUMN].min() >= split_date and len(unique_dates) > 1 :
    # All data falls into test period, and there's more than one date
    if len(unique_dates) // 2 > 0 :
        split_point_index = len(unique_dates) // 2
        split_date = pd.to_datetime(unique_dates[split_point_index])
        print(f"Warning: Initial split_date results in no training data. Splitting at midpoint: {split_date.strftime('%Y-%m-%d')}.")
    else: # Only two unique dates, use the first for training
        split_date = pd.to_datetime(unique_dates[1]) # Second date becomes start of test
        print(f"Warning: Initial split_date results in no training data. Using first date for training, split at: {split_date.strftime('%Y-%m-%d')}.")


print(f"Final split date (data before this for train, on/after for test): {split_date.strftime('%Y-%m-%d')}")


# データを分割
train_df = comprehensive_df[comprehensive_df[DATE_COLUMN] < split_date].copy()
test_df = comprehensive_df[comprehensive_df[DATE_COLUMN] >= split_date].copy()

# --- 3. Prepare features, labels, and group information ----
X_train, y_train, group_train = pd.DataFrame(), pd.Series(dtype='float64'), []
X_test, y_test, group_test = pd.DataFrame(), pd.Series(dtype='float64'), []
group_ids_train, group_ids_test = np.array([]), np.array([]) # For TensorFlow Ranking
categorical_feature_names = []

if not train_df.empty:
    categorical_feature_names = [col for col in feature_columns if train_df[col].dtype.name == 'object' or isinstance(train_df[col].dtype, pd.StringDtype) or isinstance(train_df[col].dtype, pd.CategoricalDtype)]
    for col in categorical_feature_names:
        train_df[col] = train_df[col].astype('category')
        if not test_df.empty and col in test_df.columns:
             test_df[col] = test_df[col].astype('category')

    X_train = train_df[feature_columns]
    y_train = train_df[y_label_column_name]
    # For LightGBM Ranker:
    group_train_lgbm = train_df.groupby(RACE_ID_COLUMN).size().to_list()
    # For TensorFlow Ranking (array of group_ids for each sample):
    group_ids_train = train_df[RACE_ID_COLUMN].values

    print(f"Training data: X_train {X_train.shape}, y_train {y_train.shape}")
    print(f"  LightGBM groups: {len(group_train_lgbm)} groups, TF Ranking group IDs: {len(group_ids_train)} samples")
    print(f"  Categorical features in training data: {[col for col in X_train.columns if X_train[col].dtype.name == 'category']}")

if not test_df.empty:
    X_test = test_df[feature_columns]
    y_test = test_df[y_label_column_name]
    # For LightGBM Ranker:
    group_test_lgbm = test_df.groupby(RACE_ID_COLUMN).size().to_list()
    # For TensorFlow Ranking:
    group_ids_test = test_df[RACE_ID_COLUMN].values

    print(f"Test data: X_test {X_test.shape}, y_test {y_test.shape}")
    print(f"  LightGBM groups: {len(group_test_lgbm)} groups, TF Ranking group IDs: {len(group_ids_test)} samples")
    print(f"  Categorical features in test data: {[col for col in X_test.columns if X_test[col].dtype.name == 'category']}")

if train_df.empty:
    print(f"Warning: Training data is empty. Check the split_date ({split_date.strftime('%Y-%m-%d')}) and data source.")

# Dummy prediction part for context, assuming model is trained and predicts.
# This section should be replaced with actual model prediction logic.
if not test_df.empty:
    # Create a copy for storing predictions
    test_data_with_predictions_df = test_df.copy()

    # Placeholder for actual model predictions
    # Ensure 'predicted_score' column is populated by your model
    if not test_data_with_predictions_df.empty: # Check if test_df was not empty to begin with
        # This is where your model's prediction would go.
        # For now, using random scores as a placeholder.
        test_data_with_predictions_df['predicted_score'] = np.random.rand(len(test_data_with_predictions_df))

    if 'predicted_score' in test_data_with_predictions_df.columns:
        test_data_with_predictions_df['predicted_rank'] = test_data_with_predictions_df.groupby(RACE_ID_COLUMN)['predicted_score'].rank(ascending=False, method='first')
        print("\nSample of test data with predicted scores and ranks:")
        print(test_data_with_predictions_df[[RACE_ID_COLUMN, HORSE_ID_COLUMN, 'predicted_score', 'predicted_rank', actual_rank_column_name]].head())

    else:
        print("\n'predicted_score' column is missing in test_data_with_predictions_df. Cannot calculate predicted_rank.")
elif not X_test.empty:
    print("\nTest features (X_test) exist, but no predictions were made (e.g., model not trained or test_df was empty initially).")
    print("\nモデルが学習されていない、またはテストデータの特徴量が空のため、テストデータの予測はスキップされました。")
else:
    print("\nNo test data available, skipping prediction display.")

# (train_df, test_df から X_train, y_train, group_ids_train などを生成した後)

if 'X_train' in locals():
    print(f"X_train shape: {X_train.shape}")
else:
    print("X_train is not defined.")

if 'y_train' in locals():
    print(f"y_train shape: {y_train.shape}")
else:
    print("y_train is not defined.")

if 'group_ids_train' in locals() and hasattr(group_ids_train, 'shape'): # NumPy配列を想定
    print(f"group_ids_train shape: {group_ids_train.shape}")
    if len(group_ids_train) > 0:
        print(f"First 5 elements of group_ids_train: {group_ids_train[:5]}")
        print(f"Unique groups in group_ids_train: {np.unique(group_ids_train).size}")
elif 'group_ids_train' in locals():
    print(f"group_ids_train length: {len(group_ids_train)} (type: {type(group_ids_train)})")
else:
    print("group_ids_train is not defined.")

# 同様に X_test, y_test, group_ids_test についても確認


# (test_df から X_test, y_test, group_ids_test などを生成した後)

print("\n--- Test Data Check ---")
if 'X_test' in locals() and hasattr(X_test, 'shape'):
    print(f"X_test shape: {X_test.shape}")
    if X_test.shape[0] > 0:
        print(f"X_test dtypes:\n{pd.DataFrame(X_test).info()}") # DataFrameに変換してinfo()を呼ぶか、NumPyならX_test.dtype
        # print(f"X_test NaNs: {np.isnan(X_test).any()}") # NumPyの場合
        print(f"X_test NaNs per column (if DataFrame):\n{pd.DataFrame(X_test).isnull().sum()[pd.DataFrame(X_test).isnull().sum() > 0]}")

else:
    print("X_test is not defined or has no shape attribute.")

if 'y_test' in locals() and hasattr(y_test, 'shape'):
    print(f"y_test shape: {y_test.shape}")
    if y_test.shape[0] > 0:
        print(f"y_test dtype: {y_test.dtype}")
        # print(f"y_test NaNs: {np.isnan(y_test).any()}") # NumPyの場合
        print(f"y_test NaNs (if Series): {pd.Series(y_test).isnull().sum()}")
else:
    print("y_test is not defined or has no shape attribute.")

if 'group_ids_test' in locals() and hasattr(group_ids_test, 'shape'):
    print(f"group_ids_test shape: {group_ids_test.shape}")
    if group_ids_test.shape[0] > 0:
        print(f"group_ids_test dtype: {group_ids_test.dtype}")
        print(f"First 5 elements of group_ids_test: {group_ids_test[:5]}")
        print(f"Unique groups in group_ids_test: {np.unique(group_ids_test).size}")
else:
    print("group_ids_test is not defined or has no shape attribute.")

# 行数の一致確認
if 'X_test' in locals() and 'y_test' in locals() and 'group_ids_test' in locals() and \
   hasattr(X_test, 'shape') and hasattr(y_test, 'shape') and hasattr(group_ids_test, 'shape'):
    if X_test.shape[0] == y_test.shape[0] == group_ids_test.shape[0]:
        print(f"Test data row counts match: {X_test.shape[0]}")
    else:
        print(f"ERROR: Test data row counts MISMATCH! "
              f"X_test: {X_test.shape[0]}, y_test: {y_test.shape[0]}, group_ids_test: {group_ids_test.shape[0]}")
else:
    print("Could not perform row count match for test data due to missing variables.")


# (X_test, y_test, group_ids_test が NumPy 配列として定義された後)

print(f"Before NaN removal - X_test shape: {X_test.shape}, y_test shape: {y_test.shape}, group_ids_test shape: {group_ids_test.shape}")
print(f"Number of NaNs in y_test before removal: {np.sum(np.isnan(y_test))}") # NumPy配列の場合

# y_test が NaN でない行のインデックスを取得
valid_indices = ~np.isnan(y_test)

if np.sum(~valid_indices) > 0: # NaNが存在する場合のみ処理
    print(f"Removing {np.sum(~valid_indices)} samples due to NaNs in y_test.")
    X_test = X_test[valid_indices]
    y_test = y_test[valid_indices]
    group_ids_test = group_ids_test[valid_indices]

    print(f"After NaN removal - X_test shape: {X_test.shape}, y_test shape: {y_test.shape}, group_ids_test shape: {group_ids_test.shape}")
    print(f"Number of NaNs in y_test after removal: {np.sum(np.isnan(y_test))}")
else:
    print("No NaNs found in y_test. No removal needed.")

# この後、クリーンになった X_test, y_test, group_ids_test を使って
# create_ranking_dataset やモデルの評価を行います。


import pandas as pd
import numpy as np
import sys # For X_train.info() output capture if needed, though direct print is fine.

def display_data_characteristics(data, data_name):
    """
    指定されたデータセットの特性（形状、型、NaN数など）を表示します。
    """
    print(f"--- Characteristics for {data_name} ---")
    if data is None:
        print(f"{data_name} is not defined (None).")
        return

    if not hasattr(data, 'shape'):
        print(f"{data_name} does not have a 'shape' attribute (not an array/DataFrame/Series).")
        return

    print(f"{data_name} shape: {data.shape}")

    if isinstance(data, pd.DataFrame):
        print(f"{data_name} info:")
        data.info() # Prints directly to stdout
        nan_counts = data.isnull().sum()
        nan_counts_filtered = nan_counts[nan_counts > 0]
        if not nan_counts_filtered.empty:
            print(f"{data_name} NaN counts per column:\n{nan_counts_filtered}")
        else:
            print(f"{data_name} has no NaN values in any column.")
    elif isinstance(data, pd.Series):
        print(f"{data_name} dtype: {data.dtype}")
        nan_count = data.isnull().sum()
        print(f"{data_name} NaN count: {nan_count}")
    elif isinstance(data, np.ndarray):
        print(f"{data_name} dtype: {data.dtype}")
        nan_count = 0
        if np.issubdtype(data.dtype, np.number): # For numeric types
            nan_count = np.isnan(data).sum()
        elif data.dtype == 'object': # For object types, count None or np.nan
            # Using pd.Series.isnull() is safer for object arrays that might contain non-floatable NaNs
            nan_count = pd.Series(data).isnull().sum()
        print(f"{data_name} NaN count: {nan_count}")

        # Original specific logic for group_ids_train when it's an ndarray
        if data_name == "group_ids_train" and len(data) > 0:
             print(f"First 5 elements of {data_name}: {data[:5]}")
             try:
                 # Exclude NaNs before counting unique values if data can contain NaNs
                 # pd.isna is a general way to check for NaN-like values
                 is_nan_mask = pd.isna(data)
                 if np.any(is_nan_mask):
                     unique_elements = np.unique(data[~is_nan_mask])
                 else:
                     unique_elements = np.unique(data)
                 print(f"Unique groups in {data_name}: {unique_elements.size}")
             except TypeError:
                 print(f"Could not determine unique groups in {data_name} due to uncomparable elements.")
    else:
        print(f"{data_name} is of an unrecognized type for detailed check: {type(data)}")

def filter_dataset_by_indices(data, valid_indices, data_name):
    """
    ブール型のインデックスを使用してデータセット (DataFrame, Series, or ndarray) をフィルタリングします。
    valid_indices は pandas Series または NumPy boolean 配列であることを期待します。
    """
    if data is None:
        # print(f"Info: {data_name} is None. Skipping filtering.") # Optional info message
        return None

    filter_indices_for_apply = valid_indices
    if isinstance(data, np.ndarray) and isinstance(valid_indices, pd.Series):
        filter_indices_for_apply = valid_indices.values
    
    # Ensure filter_indices_for_apply is a 1D boolean array/Series of correct length for row-wise filtering
    if not (hasattr(filter_indices_for_apply, 'ndim') and filter_indices_for_apply.ndim == 1 and 
            hasattr(filter_indices_for_apply, 'dtype') and filter_indices_for_apply.dtype == bool and
            len(filter_indices_for_apply) == data.shape[0]):
        print(f"Warning: Invalid or mismatched indices for {data_name}. Data shape: {data.shape}, Indices length: {len(filter_indices_for_apply) if hasattr(filter_indices_for_apply, '__len__') else 'N/A'}. Skipping filtering.")
        return data


    if isinstance(data, (pd.DataFrame, pd.Series)):
        try:
            return data[filter_indices_for_apply]
        except Exception as e:
            print(f"Warning: Could not filter {data_name} (type: {type(data)}). Error: {e}. Returning original.")
            return data
    elif isinstance(data, np.ndarray):
        try:
            return data[filter_indices_for_apply]
        except Exception as e:
            print(f"Warning: Could not filter {data_name} (ndarray). Error: {e}. Returning original.")
            return data
    else:
        print(f"Warning: {data_name} is not a DataFrame, Series, or ndarray (type: {type(data)}). Skipping filtering.")
        return data

# --- Training Data Check (before setting data_is_ready) ---
print("--- Training Data Check (before setting data_is_ready) ---")

# `locals().get()` を使用して、変数が未定義の場合でも安全に None を取得
# 実際のスクリプトでは、これらの変数はこのコードブロックの前に定義されていることを想定
x_train = locals().get('X_train')
y_train = locals().get('y_train')
group_ids_train = locals().get('group_ids_train')

# 1. 各データセットの初期状態表示
display_data_characteristics(x_train, "X_train")
display_data_characteristics(y_train, "y_train")
display_data_characteristics(group_ids_train, "group_ids_train")

# 2. y_train の NaN に基づくフィルタリング
y_train_is_filterable_series = y_train is not None and isinstance(y_train, pd.Series)

if y_train_is_filterable_series and y_train.isnull().any():
    num_nans_y = y_train.isnull().sum()
    print(f"\nFound {num_nans_y} NaNs in y_train. Filtering training data...")
    valid_train_indices = y_train.notna() # boolean Series

    if x_train is not None:
        print("Attempting to filter X_train...")
        x_train = filter_dataset_by_indices(x_train, valid_train_indices, "X_train")
    else:
        print("X_train is not defined, skipping its filtering based on y_train NaNs.")

    # y_train 自身をフィルタリング (pd.Series であることは確認済み)
    print("Attempting to filter y_train...")
    y_train = y_train[valid_train_indices]

    if group_ids_train is not None:
        print("Attempting to filter group_ids_train...")
        group_ids_train = filter_dataset_by_indices(group_ids_train, valid_train_indices, "group_ids_train")
    else:
        print("group_ids_train is not defined, skipping its filtering based on y_train NaNs.")

    print(f"\n--- Data Characteristics After Filtering NaNs from y_train ---")
    display_data_characteristics(x_train, "X_train (filtered)")
    display_data_characteristics(y_train, "y_train (filtered)")
    display_data_characteristics(group_ids_train, "group_ids_train (filtered)")
elif y_train_is_filterable_series: # No NaNs in y_train (and it's a Series)
    print("\nNo NaNs found in y_train. No filtering based on y_train NaNs performed.")
else: # y_train is None or not a pd.Series
    if y_train is None:
        print("\ny_train is not defined. Skipping NaN filtering based on y_train.")
    else: # Not a pd.Series
        print(f"\ny_train is not a pandas Series (type: {type(y_train)}). Skipping NaN filtering based on y_train.")


# 3. 行数の一致確認
print("\n--- Row Count Consistency Check ---")
# チェック対象の変数がすべて適切に定義されているか確認
can_check_rows = (
    x_train is not None and hasattr(x_train, 'shape') and
    y_train is not None and hasattr(y_train, 'shape') and
    group_ids_train is not None and hasattr(group_ids_train, 'shape')
)

if can_check_rows:
    shape_x = x_train.shape[0]
    shape_y = y_train.shape[0]
    shape_group = group_ids_train.shape[0]

    if shape_x == shape_y == shape_group:
        if shape_x > 0:
            print(f"Training data row counts MATCH and are non-zero: {shape_x}")
            # ここで data_is_ready = True としても良いか検討
        else: # shape_x == 0
            print(f"WARNING: Training data row counts MATCH but are zero: {shape_x}")
    else:
        print(f"ERROR: Training data row counts MISMATCH. "
              f"X_train: {shape_x}, y_train: {shape_y}, group_ids_train: {shape_group}")
else:
    print("Could not perform row count match for training data due to one or more variables being undefined, None, or not having a shape attribute.")
    # 問題のある変数を特定しやすくするための追加情報
    if x_train is None or not hasattr(x_train, 'shape'):
        print("  - X_train is missing or not an array/DataFrame.")
    if y_train is None or not hasattr(y_train, 'shape'):
        print("  - y_train is missing or not an array/Series.")
    if group_ids_train is None or not hasattr(group_ids_train, 'shape'):
        print("  - group_ids_train is missing or not an array.")

# 同様のチェックをテストデータに対しても実施
# print("\n--- Test Data Check (before setting test_data_is_ready) ---")
# X_test = locals().get('X_test')
# y_test = locals().get('y_test')
# group_ids_test = locals().get('group_ids_test')
# display_data_characteristics(X_test, "X_test")
# ... (テストデータ用の同様の処理) ...




import tensorflow as tf
import tensorflow_ranking as tfr
import tensorflow.keras.backend as K # NDCG計算の安定性向上のために追加 (オプション)
import optuna
import pandas as pd
import numpy as np
import functools
from sklearn.preprocessing import StandardScaler

# X_train, y_train, group_train, X_test, y_test, group_test, categorical_features, feature_columns, actual_rank_col, y_label_column_name, test_df は事前に定義されていると仮定します。

# 再現性のためのシード設定
tf.random.set_seed(42)
np.random.seed(42)

# --- データ前処理用関数 ---
def create_ranking_dataset(X, y, groups, list_size=10):
    """ランキング用のデータセットを作成する関数"""
    # グループごとにデータを整理
    unique_groups = np.unique(groups)
    X_list = []
    y_list = []
    
    for group in unique_groups:
        group_mask = groups == group
        group_X = X[group_mask]
        group_y = y[group_mask]
        
        # パディング処理（list_sizeに満たない場合）
        current_list_size = len(group_X) # 変数名を変更して明確化
        if current_list_size < list_size:
            # ゼロパディング
            pad_size = list_size - current_list_size
            pad_X = np.zeros((pad_size, group_X.shape[1]))
            pad_y = np.zeros(pad_size)
            
            group_X = np.vstack([group_X, pad_X])
            group_y = np.concatenate([group_y, pad_y])
        elif current_list_size > list_size:
            # 上位list_size個に制限
            top_indices = np.argsort(group_y)[::-1][:list_size]
            group_X = group_X[top_indices]
            group_y = group_y[top_indices]
        
        X_list.append(group_X)
        y_list.append(group_y)
    
    return np.array(X_list), np.array(y_list)

def create_tf_dataset(X_list, y_list, batch_size=32):
    """TensorFlowデータセットを作成"""
    dataset = tf.data.Dataset.from_tensor_slices({
        'features': X_list.astype(np.float32),
        'labels': y_list.astype(np.float32)
    })
    return dataset.batch(batch_size).prefetch(tf.data.AUTOTUNE)

# --- TF-Rankingモデル定義 ---
def create_ranking_model(input_shape, hidden_units=[64, 32], dropout_rate=0.2):
    """ランキングモデルを作成"""
    inputs = tf.keras.Input(shape=input_shape, name='features')
    
    # DNNレイヤー
    x = inputs
    for units in hidden_units:
        x = tf.keras.layers.Dense(units, activation='relu')(x)
        x = tf.keras.layers.Dropout(dropout_rate)(x)
    
    # 出力層（スコア予測）
    outputs = tf.keras.layers.Dense(1, name='scores')(x)
    
    model = tf.keras.Model(inputs=inputs, outputs=outputs)
    return model

# --- Optunaによるハイパーパラメータ最適化 ---
def objective_for_tf_ranking(trial, x_train_list, y_train_list, x_test_list, y_test_list, 
                           input_shape, list_size):
    """Optunaの目的関数。TF-Rankingのハイパーパラメータを最適化します。"""
    
    # ハイパーパラメータの定義
    params = {
        'learning_rate': trial.suggest_float('learning_rate', 1e-5, 1e-2, log=True),
        'batch_size': trial.suggest_categorical('batch_size', [16, 32, 64]),
        'hidden_units_1': trial.suggest_int('hidden_units_1', 32, 128, step=16),
        'hidden_units_2': trial.suggest_int('hidden_units_2', 16, 64, step=8),
        'dropout_rate': trial.suggest_float('dropout_rate', 0.1, 0.5, step=0.1),
        'epochs': trial.suggest_int('epochs', 10, 50, step=5)
    }
    
    # モデル作成
    model = create_ranking_model(
        input_shape=input_shape,
        hidden_units=[params['hidden_units_1'], params['hidden_units_2']],
        dropout_rate=params['dropout_rate']
    )
    
    # ランキング損失関数の設定
    ranking_loss = tfr.keras.losses.ListMLELoss()
    
    # メトリクスの設定
    ranking_metrics = [
        tfr.keras.metrics.NDCGMetric(topk=3, name='ndcg_3'),
        tfr.keras.metrics.NDCGMetric(topk=5, name='ndcg_5')
    ]
    
    # モデルコンパイル
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=params['learning_rate']),
        loss=ranking_loss,
        metrics=ranking_metrics
    )
    
    # データセット作成
    train_dataset = create_tf_dataset(x_train_list, y_train_list, params['batch_size'])
    
    if x_test_list is not None and y_test_list is not None:
        test_dataset = create_tf_dataset(x_test_list, y_test_list, params['batch_size'])
        validation_data = test_dataset
    else:
        validation_data = None
        # テストデータがない場合、Optunaはこの試行を評価できない。
        # 警告を出し、この試行が無効であることを示す値を返す。
        # あるいは、ここではエラーやTrialPrunedを発生させても良い。
        print("警告: Optunaの評価にテストデータが提供されていません。この試行の評価はスキップされます。")
        return -float('inf')
    
    # 早期停止の設定
    early_stopping = tf.keras.callbacks.EarlyStopping(
        monitor='val_ndcg_3',
        patience=5,
        restore_best_weights=True,
        mode='max'
    )
    
    # モデル学習
    try:
        history = model.fit(
            train_dataset,
            validation_data=validation_data,
            epochs=params['epochs'],
            callbacks=[early_stopping],
            verbose=0
        )
        
        # 最高のNDCG@3スコアを返す
        if validation_data:
            best_ndcg = max(history.history.get('val_ndcg_3', [-float('inf')])) # 検証NDCGがない場合は最小値を返す
        else: # 通常ここには到達しないはず
            best_ndcg = -float('inf')
        return best_ndcg
    except Exception as e:
        print(f"モデル学習中にエラーが発生しました: {e}")
        return -float('inf')

# --- データ準備とOptuna実行制御 ---
# グローバルスコープの変数が存在するかどうかを確認
data_is_ready = (
    'X_train' in globals() and not X_train.empty and
    'y_train' in globals() and not y_train.empty and
    'group_train' in globals() and len(group_train) > 0 and
    'categorical_features' in globals() and
    'feature_columns' in globals()
)

# テストデータは任意とするが、存在すればOptunaや最終評価で使用
test_data_is_ready = (
    'X_test' in globals() and not X_test.empty and
    'y_test' in globals() and not y_test.empty and
    'group_test' in globals() and len(group_test) > 0
)

USE_OPTUNA = True  # Optunaによる最適化を実行するかどうか
optimized_tf_ranker = None
best_params_from_optuna = {}
scaler = StandardScaler()

if data_is_ready:
    print("データの前処理を開始します...")
    
    # 特徴量の標準化
    X_train_scaled = scaler.fit_transform(X_train)
    if test_data_is_ready:
        X_test_scaled = scaler.transform(X_test)
    
    # ランキング用データセットの作成
    list_size = 10  # 各レースの最大馬数
    X_train_list, y_train_list = create_ranking_dataset(X_train_scaled, y_train, group_train, list_size)
    
    if test_data_is_ready:
        X_test_list, y_test_list = create_ranking_dataset(X_test_scaled, y_test, group_test, list_size)
    else:
        X_test_list, y_test_list = None, None
    
    input_shape = (list_size, X_train_scaled.shape[1])
    
    print(f"学習データの形状: {X_train_list.shape}")
    if test_data_is_ready:
        print(f"テストデータの形状: {X_test_list.shape}")

if USE_OPTUNA and data_is_ready:
    print("Optunaによるハイパーパラメータ最適化を開始します...")
    
    # functools.partial を使って目的関数に必要なデータを渡す
    objective_with_data = functools.partial(
        objective_for_tf_ranking,
        x_train_list=X_train_list,
        y_train_list=y_train_list,
        x_test_list=X_test_list if test_data_is_ready else None,
        y_test_list=y_test_list if test_data_is_ready else None,
        input_shape=input_shape,
        list_size=list_size
    )
    
    study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler(seed=42))
    study.optimize(objective_with_data, n_trials=20, timeout=1800)
    
    print("\nOptunaによるハイパーパラメータ最適化が完了しました。")
    print("最適な試行:")
    best_trial_result = study.best_trial
    print(f"  Value (NDCG@3): {best_trial_result.value}")
    print("  Params: ")
    for key, value in best_trial_result.params.items():
        print(f"    {key}: {value}")
    best_params_from_optuna = best_trial_result.params

# --- 最終モデルの学習 ---
if data_is_ready:
    print("\n最終モデルを学習します...")
    
    if USE_OPTUNA and best_params_from_optuna:
        print("Optunaで見つかった最適なパラメータを使用します。")
        final_params = best_params_from_optuna.copy()
    else:
        print("Optunaを使用しないか、最適なパラメータが見つからなかったため、デフォルトパラメータを使用します。")
        final_params = {
            'learning_rate': 0.001,
            'batch_size': 32,
            'hidden_units_1': 64,
            'hidden_units_2': 32,
            'dropout_rate': 0.2,
            'epochs': 30
        }
    
    # 最終モデル作成
    optimized_tf_ranker = create_ranking_model(
        input_shape=input_shape,
        hidden_units=[final_params['hidden_units_1'], final_params['hidden_units_2']],
        dropout_rate=final_params['dropout_rate']
    )
    
    # ランキング損失関数とメトリクスの設定
    ranking_loss = tfr.keras.losses.ListMLELoss()
    ranking_metrics = [
        tfr.keras.metrics.NDCGMetric(topk=1, name='ndcg_1'),
        tfr.keras.metrics.NDCGMetric(topk=3, name='ndcg_3'),
        tfr.keras.metrics.NDCGMetric(topk=5, name='ndcg_5')
    ]
    
    # モデルコンパイル
    optimized_tf_ranker.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=final_params['learning_rate']),
        loss=ranking_loss,
        metrics=ranking_metrics
    )
    
    # データセット作成
    train_dataset = create_tf_dataset(X_train_list, y_train_list, final_params['batch_size'])
    
    callbacks = []
    validation_data = None
    
    if test_data_is_ready:
        test_dataset = create_tf_dataset(X_test_list, y_test_list, final_params['batch_size'])
        validation_data = test_dataset
        callbacks.append(tf.keras.callbacks.EarlyStopping(
            monitor='val_ndcg_3',
            patience=10,
            restore_best_weights=True,
            mode='max',
            verbose=1
        ))
    
    # モデル学習
    history = optimized_tf_ranker.fit(
        train_dataset,
        validation_data=validation_data,
        epochs=final_params['epochs'],
        callbacks=callbacks,
        verbose=1
    )
    
    print("最終モデルの学習が完了しました。")
else:
    print("学習データが不十分なため、モデル学習は実行されませんでした。")

# --- 学習済み最終モデルでテストデータの予測 ---
if optimized_tf_ranker and test_data_is_ready:
    print("\n最終モデルでテストデータの予測を行います...")
    
    # test_data_with_predictions の準備
    if 'test_df' in globals() and not test_df.empty:
        test_data_with_predictions = test_df.copy()
    else:
        print("警告: test_df が存在しないか空のため、予測結果を格納するDataFrameを作成できません。")
        test_data_with_predictions = pd.DataFrame()
    
    if not test_data_with_predictions.empty:
        # テストデータセットで予測
        test_dataset = create_tf_dataset(X_test_list, y_test_list, 32)
        predictions = optimized_tf_ranker.predict(test_dataset)
        
        # 予測結果を元のデータフレームの各行に対応付ける
        # test_data_with_predictions (test_dfのコピー) と同じ長さのNaN配列を準備
        predicted_scores_for_df = np.full(len(test_data_with_predictions), np.nan)

        unique_test_groups = np.unique(group_test)
        for i, group in enumerate(unique_test_groups):
            original_group_mask = (group_test == group)
            original_group_indices = np.where(original_group_mask)[0] # test_dfにおける元のインデックス
            original_group_y = y_test[original_group_mask] # 元のラベル (ソート用)
            num_original_items_in_group = len(original_group_indices)
            group_scores_from_model = predictions[i].flatten() # (list_size,) の形状

            if num_original_items_in_group == 0:
                continue

            if num_original_items_in_group <= list_size:
                # パディングされたケース：元のアイテム数だけスコアを割り当てる
                predicted_scores_for_df[original_group_indices] = group_scores_from_model[:num_original_items_in_group]
            else:
                # 切り捨てられたケース：create_ranking_dataset と同じロジックで上位 list_size 個の元のインデックスを特定
                top_indices_within_group = np.argsort(original_group_y)[::-1][:list_size]
                original_indices_for_top_items = original_group_indices[top_indices_within_group]
                predicted_scores_for_df[original_indices_for_top_items] = group_scores_from_model

        # 予測結果をDataFrameに追加
        if len(predicted_scores_for_df) == len(test_data_with_predictions):
            test_data_with_predictions['predicted_score'] = predicted_scores_for_df
            test_data_with_predictions['predicted_rank'] = test_data_with_predictions.groupby('race_id')['predicted_score'].rank(ascending=False, method='first')

            print("\n予測結果（テストデータ）:")
            display_cols = ['race_id', 'horse_id', 'predicted_score', 'predicted_rank']
            if 'y_label_column_name' in globals() and y_label_column_name in test_data_with_predictions.columns:
                display_cols.append(y_label_column_name)
            if 'actual_rank_col' in globals() and actual_rank_col in test_data_with_predictions.columns:
                display_cols.append(actual_rank_col)
            
            # 実際に存在するカラムのみを選択して表示
            valid_display_cols = [col for col in display_cols if col in test_data_with_predictions.columns]
            print(test_data_with_predictions[valid_display_cols].head())
        else:
            print(f"致命的エラー: 予測スコアの割り当てに失敗しました。予測数({len(predicted_scores_for_df)})とテストデータ数({len(test_data_with_predictions)})が一致しません。")
 

elif not test_data_is_ready:
    print("\nテストデータがないため、予測はスキップされました。")
else:
    print("\nモデルが学習されていないため、テストデータの予測はスキップされました。")

# --- 評価 ---
if optimized_tf_ranker and test_data_is_ready:
    print("\nモデル評価 (検証データ):")
    test_dataset = create_tf_dataset(X_test_list, y_test_list, 32)
    evaluation_results = optimized_tf_ranker.evaluate(test_dataset, verbose=0)
    
    metric_names = optimized_tf_ranker.metrics_names
    for metric_name, value in zip(metric_names, evaluation_results):
        print(f"  {metric_name}: {value:.4f}")

# モデルサマリー表示
if optimized_tf_ranker:
    print("\nモデル構造:")
    optimized_tf_ranker.summary()

print("\nTensorFlow Rankingを使用した競馬予測システムの実行が完了しました。")

import tensorflow as tf
import tensorflow_ranking as tfr
import optuna
import pandas as pd
import numpy as np
import functools
from sklearn.preprocessing import StandardScaler
from typing import List, Tuple, Dict, Any, Optional, Union

# X_train, y_train, group_train, X_test, y_test, group_test, categorical_features, feature_columns, actual_rank_col, y_label_column_name, test_df は事前に定義されていると仮定します。
# 重要:
# - X_train, y_train, group_train は同じ行数で、各行が対応している必要があります。
# - group_train の各要素は、X_train の対応する行が属するグループ (例: レースID) を示します。
# - テストデータ (X_test, y_test, group_test) についても同様です。
# - y_train, y_test, group_train, group_test が pandas Series の場合、.values で NumPy 配列に変換して関数に渡すことを推奨します。

# --- グローバル設定値 ---
RANDOM_SEED = 42
DEFAULT_LIST_SIZE = 10  # 各レースの最大馬数 (ランキングリストのサイズ)
OPTUNA_N_TRIALS = 20    # Optunaの試行回数
OPTUNA_TIMEOUT = 1800   # Optunaのタイムアウト時間 (秒)

# Optunaを使用するかどうか
USE_OPTUNA = True

# デフォルトのハイパーパラメータ (Optunaを使用しない場合や、最適なパラメータが見つからなかった場合)
DEFAULT_HYPERPARAMETERS = {
    'learning_rate': 0.001,
    'batch_size': 32,
    'hidden_units_1': 64,
    'hidden_units_2': 32,
    'dropout_rate': 0.2,
    'epochs': 30 # Optunaの探索対象外だった場合、ここで定義
}

# 再現性のためのシード設定
tf.random.set_seed(RANDOM_SEED)
np.random.seed(RANDOM_SEED)

# --- データ前処理用関数 ---
def create_ranking_dataset(X: np.ndarray,
                           y: np.ndarray,
                           groups: np.ndarray,
                           list_size: int = DEFAULT_LIST_SIZE) -> Tuple[np.ndarray, np.ndarray]:
    """
    ランキング学習用のデータセットをリスト形式で作成します。
    各グループ（例: レース）内のアイテムを固定長 (list_size) のリストに整形します。
    不足分はゼロパディングし、超過分はyの値に基づいて上位アイテムを保持します。

    Args:
        X (np.ndarray): 特徴量データ (サンプル数 x 特徴量数)。
        y (np.ndarray): ラベルデータ (サンプル数)。ランキングの基準となるスコアや順位。
                        高いほど関連性が高い（または上位）と解釈されます。
        groups (np.ndarray): 各サンプルが属するグループID (サンプル数)。
        list_size (int): 各グループのリストの固定長。

    Returns:
        Tuple[np.ndarray, np.ndarray]:
            - X_list (np.ndarray): (グループ数 x list_size x 特徴量数) の形状の整形済み特徴量データ。
            - y_list (np.ndarray): (グループ数 x list_size) の形状の整形済みラベルデータ。
    """
    unique_groups = np.unique(groups)
    X_list_collector = []
    y_list_collector = []
    
    for group_id in unique_groups:
        group_mask = (groups == group_id)
        group_X = X[group_mask]
        group_y = y[group_mask]
        
        current_items_in_group = len(group_X)
        if current_items_in_group == 0:
            continue

        if current_items_in_group < list_size:
            # パディング処理
            pad_size = list_size - current_items_in_group
            # 特徴量の次元数 X.shape[1] を使用
            pad_X = np.zeros((pad_size, X.shape[1]))
            pad_y = np.zeros(pad_size) # ラベルは通常0でパディング
            
            processed_X = np.vstack([group_X, pad_X])
            processed_y = np.concatenate([group_y, pad_y])
        elif current_items_in_group > list_size:
            # 上位list_size個に制限 (group_y の値が大きいものを優先)
            # argsortは昇順のインデックスを返すため、[::-1]で降順にする
            top_indices = np.argsort(group_y)[::-1][:list_size]
            processed_X = group_X[top_indices]
            processed_y = group_y[top_indices]
        else: # current_items_in_group == list_size
            processed_X = group_X
            processed_y = group_y
        
        X_list_collector.append(processed_X)
        y_list_collector.append(processed_y)
    
    if not X_list_collector: # 有効なグループが一つもなかった場合
        # 特徴量の次元数を X.shape[1] から取得
        # Xが空の可能性も考慮するが、通常は学習データがある前提
        num_features = X.shape[1] if X.ndim == 2 and X.shape[1] > 0 else 1
        return np.empty((0, list_size, num_features), dtype=np.float32), np.empty((0, list_size), dtype=np.float32)

    return np.array(X_list_collector), np.array(y_list_collector)

def create_tf_dataset(X_list: np.ndarray,
                      y_list: np.ndarray,
                      batch_size: int = 32) -> tf.data.Dataset:
    """
    整形済みのリスト形式データからTensorFlowデータセットを作成します。

    Args:
        X_list (np.ndarray): (グループ数 x list_size x 特徴量数) の特徴量データ。
        y_list (np.ndarray): (グループ数 x list_size) のラベルデータ。
        batch_size (int): データセットのバッチサイズ。

    Returns:
        tf.data.Dataset: バッチ化され、プリフェッチが設定されたTensorFlowデータセット。
    """
    dataset = tf.data.Dataset.from_tensor_slices({
        'features': X_list.astype(np.float32),
        'labels': y_list.astype(np.float32) # TF-Rankingはラベルもfloat32を期待することが多い
    })
    return dataset.batch(batch_size).prefetch(tf.data.AUTOTUNE)

# --- TF-Rankingモデル定義 ---
def create_ranking_model(input_shape: Tuple[int, int],
                         hidden_units: List[int] = [64, 32],
                         dropout_rate: float = 0.2) -> tf.keras.Model:
    """
    TensorFlow Ranking用のDNNベースのスコアリングモデルを作成します。
    このモデルは各アイテムのスコアを予測します。

    Args:
        input_shape (Tuple[int, int]): モデルへの入力形状 (list_size, num_features)。
        hidden_units (List[int]): DNNの隠れ層のユニット数のリスト。
        dropout_rate (float): ドロップアウト率。

    Returns:
        tf.keras.Model: コンパイル前のランキングモデル。
    """
    inputs = tf.keras.Input(shape=input_shape, name='features') # (batch_size, list_size, num_features)
    
    # DNNレイヤー: TimeDistributed を使ってリスト内の各アイテムに同じDNNを適用
    # または、入力形状を (list_size * num_features) にフラット化して処理することも考えられるが、
    # ここでは各アイテムの特徴ベクトルを独立して処理するアプローチを示す。
    # TF-Ranking の損失関数は通常 (batch_size, list_size) のスコアを期待する。
    # このモデルは (batch_size, list_size, 1) のスコアを出力し、損失関数側で調整される。
    
    x = inputs # (batch_size, list_size, num_features)
    for units_count in hidden_units:
        # Denseレイヤーはデフォルトで最後の次元に作用する。
        # (batch_size, list_size, num_features) -> (batch_size, list_size, units_count)
        x = tf.keras.layers.Dense(units_count, activation='relu')(x)
        x = tf.keras.layers.Dropout(dropout_rate)(x)
    
    # 出力層（各アイテムのスコア予測）
    # (batch_size, list_size, units_count) -> (batch_size, list_size, 1)
    outputs = tf.keras.layers.Dense(1, name='scores')(x)
    
    model = tf.keras.Model(inputs=inputs, outputs=outputs)
    return model

# --- Optunaによるハイパーパラメータ最適化 ---
def objective_for_tf_ranking(trial: optuna.trial.Trial,
                             x_train_list_opt: np.ndarray, # 変数名を変更 (グローバルと区別)
                             y_train_list_opt: np.ndarray, # 変数名を変更
                             input_shape_opt: Tuple[int, int], # 変数名を変更
                             x_test_list_opt: Optional[np.ndarray] = None, # 変数名を変更
                             y_test_list_opt: Optional[np.ndarray] = None) -> float: # 変数名を変更
    """
    Optunaの目的関数。TF-Rankingモデルのハイパーパラメータを最適化し、
    検証データセットでのNDCG@3を最大化します。

    Args:
        trial (optuna.trial.Trial): OptunaのTrialオブジェクト。
        x_train_list_opt, y_train_list_opt: 学習用の整形済みデータ。
        input_shape_opt: モデルの入力形状。
        x_test_list_opt, y_test_list_opt: (オプション) 検証用の整形済みデータ。

    Returns:
        float: 検証データセットでの最高のNDCG@3スコア。テストデータがない場合は -float('inf')。
    """
    params = {
        'learning_rate': trial.suggest_float('learning_rate', 1e-5, 1e-2, log=True),
        'batch_size': trial.suggest_categorical('batch_size', [16, 32, 64]),
        'hidden_units_1': trial.suggest_int('hidden_units_1', 32, 128, step=16),
        'hidden_units_2': trial.suggest_int('hidden_units_2', 16, 64, step=8),
        'dropout_rate': trial.suggest_float('dropout_rate', 0.1, 0.5, step=0.1),
        'epochs': trial.suggest_int('epochs', 10, 50, step=5) # epochsも最適化対象に
    }
    
    model = create_ranking_model(
        input_shape=input_shape_opt,
        hidden_units=[params['hidden_units_1'], params['hidden_units_2']],
        dropout_rate=params['dropout_rate']
    )
    
    ranking_loss_fn = tfr.keras.losses.ListMLELoss() # TF Rankingは (batch_size, list_size) のラベルを期待
    
    ranking_metrics_fns = [
        tfr.keras.metrics.NDCGMetric(topk=3, name='ndcg_3'),
        tfr.keras.metrics.NDCGMetric(topk=5, name='ndcg_5')
    ]
    
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=params['learning_rate']),
        loss=ranking_loss_fn,
        metrics=ranking_metrics_fns
    )
    
    train_dataset_opt = create_tf_dataset(x_train_list_opt, y_train_list_opt, params['batch_size'])
    
    validation_data_opt = None
    if x_test_list_opt is not None and y_test_list_opt is not None and len(x_test_list_opt) > 0:
        test_dataset_opt = create_tf_dataset(x_test_list_opt, y_test_list_opt, params['batch_size'])
        validation_data_opt = test_dataset_opt
    else:
        print("警告: Optunaの評価に有効なテストデータが提供されていません。この試行の評価はスキップされます。")
        # Optunaにこの試行を枝刈りするよう促すこともできる
        # raise optuna.exceptions.TrialPruned("Validation data not available.")
        return -float('inf') 
    
    early_stopping_cb = tf.keras.callbacks.EarlyStopping(
        monitor='val_ndcg_3', # 監視するメトリクス
        patience=5,           # 改善が見られない場合に待つエポック数
        restore_best_weights=True, # 最良の重みを復元
        mode='max'            # ndcgは大きい方が良い
    )
    
    try:
        history = model.fit(
            train_dataset_opt,
            validation_data=validation_data_opt,
            epochs=params['epochs'],
            callbacks=[early_stopping_cb],
            verbose=0 # Optuna実行中はログを抑制
        )
        
        # history.history['val_ndcg_3'] がリストであることを確認
        val_ndcg_3_scores = history.history.get('val_ndcg_3', [])
        if not val_ndcg_3_scores: # リストが空の場合 (学習が1エポックも完了しなかった等)
             best_ndcg_val = -float('inf')
        else:
             best_ndcg_val = max(val_ndcg_3_scores)
        return best_ndcg_val
    except Exception as e:
        print(f"Optuna試行中にモデル学習エラーが発生しました: {e}")
        return -float('inf') # エラー発生時は低いスコアを返す

# --- データ準備とOptuna実行制御 ---
# グローバルスコープの変数が存在するかどうかを確認 (Jupyter Notebook等での実行を想定)
# これらの変数はスクリプト実行前に定義されている必要がある
# 例:
# X_train = pd.DataFrame(...)
# y_train = pd.Series(...)
# group_train = pd.Series(...) (レースIDなど)
# ... testデータも同様 ...
# feature_columns = [...] (特徴量のカラム名リスト)
# test_df = pd.DataFrame(...) (予測結果をマージするためのテストデータフレーム)

data_is_ready = (
    'X_train' in globals() and isinstance(X_train, pd.DataFrame) and not X_train.empty and
    'y_train' in globals() and isinstance(y_train, pd.Series) and not y_train.empty and
    'group_train' in globals() and isinstance(group_train, pd.Series) and not group_train.empty and
    len(X_train) == len(y_train) == len(group_train) # 行数が一致しているか
)

test_data_is_ready = (
    'X_test' in globals() and isinstance(X_test, pd.DataFrame) and not X_test.empty and
    'y_test' in globals() and isinstance(y_test, pd.Series) and not y_test.empty and
    'group_test' in globals() and isinstance(group_test, pd.Series) and not group_test.empty and
    len(X_test) == len(y_test) == len(group_test) # 行数が一致しているか
)

optimized_tf_ranker: Optional[tf.keras.Model] = None
best_params_from_optuna: Dict[str, Any] = {}
scaler = StandardScaler()

if data_is_ready:
    print("データの前処理を開始します...")
    
    # 特徴量の標準化 (X_train, X_test は DataFrame を想定)
    # feature_columns が定義されている前提
    if 'feature_columns' not in globals():
        print("エラー: 特徴量カラムリスト 'feature_columns' が定義されていません。")
        # 処理を中断するか、適切なデフォルト処理を行う
        feature_columns = X_train.columns.tolist() # フォールバックとして全カラムを使用 (非推奨)

    X_train_processed = X_train[feature_columns].copy()
    X_train_scaled_np: np.ndarray = scaler.fit_transform(X_train_processed)
    
    y_train_np: np.ndarray = y_train.values
    group_train_np: np.ndarray = group_train.values

    if test_data_is_ready:
        X_test_processed = X_test[feature_columns].copy()
        X_test_scaled_np: np.ndarray = scaler.transform(X_test_processed)
        y_test_np: np.ndarray = y_test.values
        group_test_np: np.ndarray = group_test.values
    
    # ランキング用データセットの作成
    # ここで渡す group_train_np は X_train_scaled_np の各行に対応するグループID
    X_train_list, y_train_list = create_ranking_dataset(
        X_train_scaled_np, y_train_np, group_train_np, DEFAULT_LIST_SIZE
    )
    
    X_test_list_main: Optional[np.ndarray] = None # mainスコープでの変数名
    y_test_list_main: Optional[np.ndarray] = None # mainスコープでの変数名
    if test_data_is_ready:
        X_test_list_main, y_test_list_main = create_ranking_dataset(
            X_test_scaled_np, y_test_np, group_test_np, DEFAULT_LIST_SIZE
        )
        if X_test_list_main is not None and len(X_test_list_main) == 0: # 有効なテストグループがなかった場合
            print("警告: テストデータから有効なランキングリストが作成できませんでした。テストはスキップされます。")
            X_test_list_main, y_test_list_main = None, None # Optunaや学習で使われないようにする
            test_data_is_ready = False # 後続の処理でテストデータがないものとして扱う
    
    # 入力形状は (list_size, num_features)
    input_shape_main: Tuple[int, int] = (DEFAULT_LIST_SIZE, X_train_scaled_np.shape[1])
    
    print(f"学習データの形状 (X_list): {X_train_list.shape}, (y_list): {y_train_list.shape}")
    if test_data_is_ready and X_test_list_main is not None:
        print(f"テストデータの形状 (X_list): {X_test_list_main.shape}, (y_list): {y_test_list_main.shape}")
    elif not test_data_is_ready:
        print("テストデータは利用できません。")


    if USE_OPTUNA and data_is_ready: # data_is_ready は学習データがあることを保証
        print("Optunaによるハイパーパラメータ最適化を開始します...")
        
        # Optunaの目的関数に渡すデータ。テストデータがない場合はNoneを渡す。
        objective_with_data = functools.partial(
            objective_for_tf_ranking,
            x_train_list_opt=X_train_list,
            y_train_list_opt=y_train_list,
            input_shape_opt=input_shape_main,
            x_test_list_opt=X_test_list_main if test_data_is_ready else None,
            y_test_list_opt=y_test_list_main if test_data_is_ready else None
        )
        
        study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler(seed=RANDOM_SEED))
        study.optimize(objective_with_data, n_trials=OPTUNA_N_TRIALS, timeout=OPTUNA_TIMEOUT)
        
        print("\nOptunaによるハイパーパラメータ最適化が完了しました。")
        if study.best_trial:
            print("最適な試行:")
            best_trial_result = study.best_trial
            print(f"  Value (NDCG@3): {best_trial_result.value}")
            print("  Params: ")
            for key, value in best_trial_result.params.items():
                print(f"    {key}: {value}")
            best_params_from_optuna = best_trial_result.params
        else:
            print("Optunaで有効な試行が見つかりませんでした。")

    # --- 最終モデルの学習 ---
    if data_is_ready: # data_is_ready は学習データがあることを保証
        print("\n最終モデルを学習します...")
        
        final_params_main = DEFAULT_HYPERPARAMETERS.copy() # デフォルト値で初期化
        if USE_OPTUNA and best_params_from_optuna:
            print("Optunaで見つかった最適なパラメータを使用します。")
            final_params_main.update(best_params_from_optuna) # Optunaの結果で上書き
        else:
            print("Optunaを使用しないか、最適なパラメータが見つからなかったため、デフォルトパラメータを使用します。")
        
        optimized_tf_ranker = create_ranking_model(
            input_shape=input_shape_main,
            hidden_units=[final_params_main['hidden_units_1'], final_params_main['hidden_units_2']],
            dropout_rate=final_params_main['dropout_rate']
        )
        
        final_ranking_loss_fn = tfr.keras.losses.ListMLELoss()
        final_ranking_metrics_fns = [
            tfr.keras.metrics.NDCGMetric(topk=1, name='ndcg_1'),
            tfr.keras.metrics.NDCGMetric(topk=3, name='ndcg_3'),
            tfr.keras.metrics.NDCGMetric(topk=5, name='ndcg_5')
        ]
        
        optimized_tf_ranker.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=final_params_main['learning_rate']),
            loss=final_ranking_loss_fn,
            metrics=final_ranking_metrics_fns
        )
        
        final_train_dataset = create_tf_dataset(X_train_list, y_train_list, final_params_main['batch_size'])
        
        callbacks_main = []
        validation_data_main = None
        
        if test_data_is_ready and X_test_list_main is not None and y_test_list_main is not None:
            final_test_dataset = create_tf_dataset(X_test_list_main, y_test_list_main, final_params_main['batch_size'])
            validation_data_main = final_test_dataset
            callbacks_main.append(tf.keras.callbacks.EarlyStopping(
                monitor='val_ndcg_3',
                patience=10, # Optunaより少し長めのpatience
                restore_best_weights=True,
                mode='max',
                verbose=1
            ))
        
        print(f"最終モデルの学習を開始します... Epochs: {final_params_main['epochs']}")
        history_final = optimized_tf_ranker.fit(
            final_train_dataset,
            validation_data=validation_data_main,
            epochs=final_params_main['epochs'],
            callbacks=callbacks_main,
            verbose=1
        )
        print("最終モデルの学習が完了しました。")

else: # data_is_ready が False の場合
    print("学習データが不十分または不整合なため、モデル学習は実行されませんでした。")
    print("X_train, y_train, group_train の存在と、それらの行数が一致しているか確認してください。")

# --- 学習済み最終モデルでテストデータの予測 ---
# test_df は予測結果を格納・表示するために使用
if optimized_tf_ranker and test_data_is_ready and 'test_df' in globals() and isinstance(test_df, pd.DataFrame) and not test_df.empty:
    print("\n最終モデルでテストデータの予測を行います...")
    
    # X_test_list_main, y_test_list_main が None でないことを確認
    if X_test_list_main is None or y_test_list_main is None:
        print("警告: 予測に必要なテストデータリスト (X_test_list_main, y_test_list_main) がありません。予測をスキップします。")
    else:
        test_data_with_predictions = test_df.copy()
        
        # バッチサイズは学習時と同じか、推論に適した値を使用
        # final_params_main が定義されている前提
        predict_batch_size = final_params_main.get('batch_size', 32)
        predict_dataset = create_tf_dataset(X_test_list_main, y_test_list_main, predict_batch_size)
        
        # predictions_raw の形状は (num_test_groups, list_size, 1)
        predictions_raw = optimized_tf_ranker.predict(predict_dataset)
        
        # 予測結果を元の test_df の各行に対応付ける
        # group_test_np, y_test_np は元のテストデータに対応
        predicted_scores_flat = np.full(len(test_data_with_predictions), np.nan)
        
        unique_test_groups = np.unique(group_test_np) # group_test_np を使用
        
        current_pred_idx = 0 # predictions_raw のインデックス
        for group_val in unique_test_groups:
            if current_pred_idx >= len(predictions_raw):
                print(f"警告: グループ {group_val} の予測データがありません。スキップします。")
                break

            # 元の test_df/X_test における、このグループのアイテムのインデックス
            original_indices_in_df = test_df[group_train == group_val].index # group_train は Series を想定
            
            # create_ranking_dataset と同じロジックで処理されたアイテム数を特定
            # X_test_scaled_np, y_test_np, group_test_np を使用
            original_group_mask_np = (group_test_np == group_val)
            num_original_items_in_group = np.sum(original_group_mask_np)
            
            # モデルからの予測スコア (このグループのリストに対応)
            # predictions_raw[current_pred_idx] の形状は (list_size, 1) なので flatten()
            group_scores_from_model = predictions_raw[current_pred_idx].flatten()
            
            if num_original_items_in_group == 0:
                current_pred_idx +=1
                continue

            if num_original_items_in_group <= DEFAULT_LIST_SIZE:
                # パディングされたケース: 元のアイテム数だけスコアを割り当てる
                # original_indices_in_df の順序と group_scores_from_model の先頭部分が対応
                # ただし、create_ranking_dataset でソートされていない場合
                # ここでは、元の順序が保たれていると仮定して、先頭から割り当てる
                # より堅牢にするには、create_ranking_datasetでアイテムIDも保持し、突合する
                target_indices = original_indices_in_df[:num_original_items_in_group]
                predicted_scores_flat[target_indices] = group_scores_from_model[:num_original_items_in_group]

            else: # num_original_items_in_group > DEFAULT_LIST_SIZE
                # 切り捨てられたケース: create_ranking_dataset と同じロジックで
                # y_test_np に基づいて上位 DEFAULT_LIST_SIZE 個が選ばれたはず。
                # それらの元のインデックスを特定する。
                original_y_for_group = y_test_np[original_group_mask_np]
                
                # y_test_np の中で、このグループの上位アイテムのインデックス (グループ内での相対インデックス)
                top_indices_within_original_group = np.argsort(original_y_for_group)[::-1][:DEFAULT_LIST_SIZE]
                
                # test_df全体での絶対インデックスを取得
                # original_indices_in_df は DataFrame のインデックスなので、これを使って NumPy 配列のインデックスに変換
                # original_indices_in_df.take(top_indices_within_original_group)
                # または、X_test_scaled_np でのインデックスを元に DataFrame のインデックスを取得
                
                # X_test_scaled_np での絶対インデックス
                absolute_indices_in_X_test = np.where(original_group_mask_np)[0]
                # その中で上位だったものの絶対インデックス
                target_absolute_indices = absolute_indices_in_X_test[top_indices_within_original_group]
                
                # DataFrame のインデックスに変換 (X_test と test_df のインデックスが一致している前提)
                target_df_indices = test_df.iloc[target_absolute_indices].index
                predicted_scores_flat[target_df_indices] = group_scores_from_model # 長さが一致するはず

            current_pred_idx += 1

        if len(predicted_scores_flat) == len(test_data_with_predictions):
            test_data_with_predictions['predicted_score'] = predicted_scores_flat
            # 'race_id' カラムが存在する前提でランク付け
            if 'race_id' in test_data_with_predictions.columns:
                 test_data_with_predictions['predicted_rank'] = test_data_with_predictions.groupby('race_id')['predicted_score'].rank(ascending=False, method='first')
            else:
                 print("警告: 'race_id' カラムが test_df に見つからないため、predicted_rank は計算されません。")


            print("\n予測結果（テストデータの一部）:")
            display_cols = ['predicted_score'] # 必須
            if 'race_id' in test_data_with_predictions.columns: display_cols.insert(0, 'race_id')
            if 'horse_id' in test_data_with_predictions.columns: display_cols.insert(1, 'horse_id') # 例
            if 'predicted_rank' in test_data_with_predictions.columns: display_cols.append('predicted_rank')

            # y_label_column_name と actual_rank_col はグローバルスコープで定義されている想定
            if 'y_label_column_name' in globals() and y_label_column_name in test_data_with_predictions.columns:
                display_cols.append(y_label_column_name)
            if 'actual_rank_col' in globals() and actual_rank_col in test_data_with_predictions.columns:
                display_cols.append(actual_rank_col)
            
            valid_display_cols = [col for col in display_cols if col in test_data_with_predictions.columns]
            print(test_data_with_predictions[valid_display_cols].head())
        else:
            print(f"致命的エラー: 予測スコアの割り当てに失敗しました。予測数({len(predicted_scores_flat)})とテストデータ数({len(test_data_with_predictions)})が一致しません。")
elif not test_data_is_ready:
    print("\nテストデータがないか不整合なため、予測はスキップされました。")
elif not optimized_tf_ranker:
     print("\nモデルが学習されていないため、テストデータの予測はスキップされました。")
else: # test_df がない場合など
    print("\n予測結果を格納する test_df がないため、予測詳細はスキップされました。")


# --- 評価 ---
if optimized_tf_ranker and test_data_is_ready and X_test_list_main is not None and y_test_list_main is not None:
    print("\nモデル評価 (テストデータ):")
    # final_params_main が定義されている前提
    eval_batch_size = final_params_main.get('batch_size', 32)
    eval_dataset = create_tf_dataset(X_test_list_main, y_test_list_main, eval_batch_size)
    evaluation_results = optimized_tf_ranker.evaluate(eval_dataset, verbose=0)
    
    metric_names = optimized_tf_ranker.metrics_names
    for metric_name, value in zip(metric_names, evaluation_results):
        print(f"  {metric_name}: {value:.4f}")

# モデルサマリー表示
if optimized_tf_ranker:
    print("\n最終モデル構造:")
    optimized_tf_ranker.summary()

print("\nTensorFlow Rankingを使用した競馬予測システムの実行が完了しました。")



