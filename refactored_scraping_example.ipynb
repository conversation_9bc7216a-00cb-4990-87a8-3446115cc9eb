import os
import re
import sys
import random

import pandas as pd
from tqdm.auto import tqdm

sys.path.append('.')  # refactored_scrap.pyが同じディレクトリにある場合
from module.refactored_scrap import (
    scrape_netkeiba_race_dates,
    scrape_netkeiba_race_ids,
    scrape_html_race,
    scrape_html_horse,
    scrape_html_ped,
    scrape_html_horse_with_master
)
from module.constants import USER_AGENTS, BASE_HEADERS, UrlPaths, LocalPaths, REQUEST_TIMEOUT
from module.extract_horse_ids import extract_horse_ids_from_race_data

# デバッグモードを有効にする場合はTrue（詳細なログが出力されます）
# 通常の実行では False に設定することで、重要なログのみ表示されます
setup_logger(debug_level=True)

# 例: 2024年1月〜2024年2月の開催日を取得
from_date = '2024-01-01'
to_date = '2024-03-01'
kaisai_dates = scrape_netkeiba_race_dates(from_date, to_date)
print(f'取得した開催日数: {len(kaisai_dates)}')
print(kaisai_dates[:5])  # 最初の5件だけ表示

# 例: 最初の開催日だけでレースIDを取得
if kaisai_dates:
    try:
        race_ids = scrape_netkeiba_race_ids([kaisai_dates[0]], debug=True)
        if race_ids:
            print(f'取得したレースID数: {len(race_ids)}')
            print(race_ids)
        else:
            print('レースIDが取得できませんでした。ネットワークやWebDriverの設定を確認してください。')
    except Exception as e:
        print(f'エラーが発生しました: {e}')
else:
    print('開催日が取得できませんでした')

# 最初の3つのレースIDだけを使用
if race_ids and len(race_ids) > 0:
    target_race_ids = race_ids[:3]
    print(f'スクレイピング対象のレースID: {target_race_ids}')
    
    # レース情報のHTMLをスクレイピング
    race_html_paths = scrape_html_race(target_race_ids, skip=True)
    print(f'スクレイピングしたレース情報: {len(race_html_paths)}件')
    print(race_html_paths)
else:
    print('スクレイピング対象のレースIDがありません')

horse_ids = extract_horse_ids_from_race_data(year=2019)
# 馬情報のHTMLをスクレイピング
horse_html_paths = scrape_html_horse(horse_ids, skip=True)
print(f'スクレイピングした馬情報: {len(horse_html_paths)}件')
print(horse_html_paths)

horse_ids = extract_horse_ids_from_race_data(year=2020)
# 馬血統情報のHTMLをスクレイピング
horse_html_paths = scrape_html_ped(horse_ids, skip=True)
print(f'スクレイピングした馬血統情報: {len(horse_html_paths)}件')
print(horse_html_paths)